import { TradingPair } from '../types/types';

export const tradingPairs: TradingPair[] = [
  {
    symbol: 'BTCUSDT',
    name: 'BTC/USDT',
    fullName: 'Bitcoin'
  },
  {
    symbol: 'ETHUSDT',
    name: 'ETH/USDT',
    fullName: 'Ethereum'
  },
  {
    symbol: 'BNBUSDT',
    name: 'BNB/USDT',
    fullName: 'Binance Coin'
  },
  {
    symbol: 'SOLUSDT',
    name: 'SOL/USDT',
    fullName: 'Solana'
  },
  {
    symbol: 'ADAUSDT',
    name: 'ADA/USDT',
    fullName: 'Cardano'
  },
  {
    symbol: 'DOGEUSDT',
    name: 'DOGE/USDT',
    fullName: 'Dogecoin'
  }
];