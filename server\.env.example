# Database Configuration
DB_URL=mongodb://localhost:27017/your-database-name
DB_NAME=your-database-name

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_ISSUER=your-app-name
JWT_AUDIENCE=your-app-users

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER_NAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
BRAND_NAME=Your App Name

# OTPless Configuration
OTPLESS_CLIENT_ID=your-otpless-client-id
OTPLESS_CLIENT_SECRET=your-otpless-client-secret
OTPLESS_API_KEY=your-otpless-api-key

# Admin Configuration
ADMIN_EMAILS=<EMAIL>

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Base URL
BASE_URL=http://localhost:5000

# Password Security
PASSWORD_PEPPER=your-password-pepper-value

# Other configurations...
