'use strict';

const logger = require('../services/logger');
const log = new logger('VulnerabilityScanner').getChildLogger();
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

/**
 * Advanced Vulnerability Scanner
 * Performs automated security testing and vulnerability detection
 */
class VulnerabilityScanner {
    constructor() {
        this.baseUrl = process.env.SCANNER_BASE_URL || 'http://localhost:3000';
        this.testResults = [];
        this.vulnerabilities = [];
        this.scanConfig = {
            timeout: 30000,
            maxRetries: 3,
            userAgent: 'HyperTradeAI-Security-Scanner/1.0'
        };
    }

    /**
     * Run comprehensive security scan
     */
    async runFullScan() {
        log.info('Starting comprehensive security scan...');
        
        try {
            // Authentication vulnerabilities
            await this.scanAuthentication();
            
            // Input validation vulnerabilities
            await this.scanInputValidation();
            
            // Authorization vulnerabilities
            await this.scanAuthorization();
            
            // Payment system vulnerabilities
            await this.scanPaymentSecurity();
            
            // API security vulnerabilities
            await this.scanApiSecurity();
            
            // File upload vulnerabilities
            await this.scanFileUpload();
            
            // Session management vulnerabilities
            await this.scanSessionManagement();
            
            // Generate comprehensive report
            const report = await this.generateSecurityReport();
            
            log.info('Security scan completed');
            return report;
            
        } catch (error) {
            log.error('Error during security scan:', error);
            throw error;
        }
    }

    /**
     * Scan authentication vulnerabilities
     */
    async scanAuthentication() {
        log.info('Scanning authentication vulnerabilities...');
        
        const tests = [
            this.testSQLInjectionLogin(),
            this.testNoSQLInjectionLogin(),
            this.testBruteForceProtection(),
            this.testJWTVulnerabilities(),
            this.testPasswordPolicyBypass(),
            this.test2FABypass(),
            this.testSessionFixation(),
            this.testAccountEnumeration()
        ];
        
        const results = await Promise.allSettled(tests);
        this.processTestResults('authentication', results);
    }

    /**
     * Test SQL/NoSQL injection in login
     */
    async testSQLInjectionLogin() {
        const payloads = [
            { email: "admin'--", password: "password" },
            { email: "admin' OR '1'='1", password: "password" },
            { email: { "$ne": null }, password: { "$ne": null } },
            { email: { "$gt": "" }, password: { "$gt": "" } },
            { email: "admin'; DROP TABLE users;--", password: "password" }
        ];
        
        for (const payload of payloads) {
            try {
                const response = await this.makeRequest('POST', '/api/user/login', payload);
                
                if (response.status === 200 && response.data.status === true) {
                    this.addVulnerability({
                        type: 'SQL/NoSQL Injection',
                        severity: 'Critical',
                        endpoint: '/api/user/login',
                        payload: payload,
                        description: 'Authentication bypass via injection attack',
                        impact: 'Complete authentication bypass, potential data breach'
                    });
                }
            } catch (error) {
                // Expected for most payloads
            }
        }
    }

    /**
     * Test NoSQL injection in login
     */
    async testNoSQLInjectionLogin() {
        const payloads = [
            { email: { "$regex": ".*" }, password: { "$regex": ".*" } },
            { email: { "$where": "this.email" }, password: "password" },
            { email: { "$exists": true }, password: { "$exists": true } }
        ];
        
        for (const payload of payloads) {
            try {
                const response = await this.makeRequest('POST', '/api/user/login', payload);
                
                if (response.status === 200 && response.data.status === true) {
                    this.addVulnerability({
                        type: 'NoSQL Injection',
                        severity: 'Critical',
                        endpoint: '/api/user/login',
                        payload: payload,
                        description: 'NoSQL injection allows authentication bypass',
                        impact: 'Authentication bypass, unauthorized access'
                    });
                }
            } catch (error) {
                // Expected for most payloads
            }
        }
    }

    /**
     * Test brute force protection
     */
    async testBruteForceProtection() {
        const testEmail = '<EMAIL>';
        let blockedAfterAttempts = 0;
        
        for (let i = 0; i < 20; i++) {
            try {
                const response = await this.makeRequest('POST', '/api/user/login', {
                    email: testEmail,
                    password: `wrongpassword${i}`
                });
                
                if (response.status === 429 || response.data.message?.includes('blocked')) {
                    blockedAfterAttempts = i + 1;
                    break;
                }
            } catch (error) {
                if (error.response?.status === 429) {
                    blockedAfterAttempts = i + 1;
                    break;
                }
            }
        }
        
        if (blockedAfterAttempts === 0 || blockedAfterAttempts > 10) {
            this.addVulnerability({
                type: 'Insufficient Brute Force Protection',
                severity: 'High',
                endpoint: '/api/user/login',
                description: `No rate limiting detected after ${blockedAfterAttempts || 20} attempts`,
                impact: 'Brute force attacks possible, credential stuffing'
            });
        }
    }

    /**
     * Test JWT vulnerabilities
     */
    async testJWTVulnerabilities() {
        // Test algorithm confusion
        const noneAlgToken = this.createJWTWithAlgorithm('none', { sub: 'admin', role: 'admin' });
        
        try {
            const response = await this.makeRequest('GET', '/api/user/profile', {}, {
                'Authorization': `Bearer ${noneAlgToken}`
            });
            
            if (response.status === 200) {
                this.addVulnerability({
                    type: 'JWT Algorithm Confusion',
                    severity: 'Critical',
                    endpoint: '/api/user/profile',
                    description: 'JWT accepts "none" algorithm, allowing token forgery',
                    impact: 'Complete authentication bypass, privilege escalation'
                });
            }
        } catch (error) {
            // Expected
        }
        
        // Test weak secret
        const weakSecrets = ['secret', '123456', 'password', 'jwt-secret'];
        for (const secret of weakSecrets) {
            const token = this.createJWTWithSecret(secret, { sub: 'admin', role: 'admin' });
            
            try {
                const response = await this.makeRequest('GET', '/api/user/profile', {}, {
                    'Authorization': `Bearer ${token}`
                });
                
                if (response.status === 200) {
                    this.addVulnerability({
                        type: 'Weak JWT Secret',
                        severity: 'Critical',
                        endpoint: '/api/user/profile',
                        description: `JWT uses weak secret: ${secret}`,
                        impact: 'Token forgery, authentication bypass'
                    });
                }
            } catch (error) {
                // Expected
            }
        }
    }

    /**
     * Scan input validation vulnerabilities
     */
    async scanInputValidation() {
        log.info('Scanning input validation vulnerabilities...');
        
        const tests = [
            this.testXSSVulnerabilities(),
            this.testCommandInjection(),
            this.testPathTraversal(),
            this.testXMLInjection(),
            this.testLDAPInjection()
        ];
        
        const results = await Promise.allSettled(tests);
        this.processTestResults('input_validation', results);
    }

    /**
     * Test XSS vulnerabilities
     */
    async testXSSVulnerabilities() {
        const xssPayloads = [
            '<script>alert("XSS")</script>',
            '"><script>alert("XSS")</script>',
            'javascript:alert("XSS")',
            '<img src=x onerror=alert("XSS")>',
            '<svg onload=alert("XSS")>',
            '${alert("XSS")}',
            '{{alert("XSS")}}'
        ];
        
        const endpoints = [
            { method: 'POST', path: '/api/support', field: 'message' },
            { method: 'POST', path: '/api/user/profile', field: 'name' },
            { method: 'POST', path: '/api/user/profile', field: 'bio' }
        ];
        
        for (const endpoint of endpoints) {
            for (const payload of xssPayloads) {
                try {
                    const data = { [endpoint.field]: payload };
                    const response = await this.makeRequest(endpoint.method, endpoint.path, data);
                    
                    if (response.data && JSON.stringify(response.data).includes(payload)) {
                        this.addVulnerability({
                            type: 'Cross-Site Scripting (XSS)',
                            severity: 'High',
                            endpoint: endpoint.path,
                            payload: payload,
                            description: `Reflected XSS in ${endpoint.field} parameter`,
                            impact: 'Session hijacking, credential theft, malicious actions'
                        });
                    }
                } catch (error) {
                    // Expected for most payloads
                }
            }
        }
    }

    /**
     * Scan payment system vulnerabilities
     */
    async scanPaymentSecurity() {
        log.info('Scanning payment system vulnerabilities...');
        
        const tests = [
            this.testRaceConditions(),
            this.testAmountManipulation(),
            this.testWithdrawalBypass(),
            this.testDoubleSpending(),
            this.testFeeManipulation()
        ];
        
        const results = await Promise.allSettled(tests);
        this.processTestResults('payment_security', results);
    }

    /**
     * Test race conditions in withdrawals
     */
    async testRaceConditions() {
        // This would require a valid test account with balance
        // Implementation would test concurrent withdrawal requests
        
        const testData = {
            amount: 100,
            address: '0x1234567890123456789012345678901234567890'
        };
        
        // Simulate concurrent requests
        const promises = Array(5).fill().map(() => 
            this.makeRequest('POST', '/api/add-withdrawal', testData)
        );
        
        try {
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled' && r.value.status === 200);
            
            if (successful.length > 1) {
                this.addVulnerability({
                    type: 'Race Condition',
                    severity: 'Critical',
                    endpoint: '/api/add-withdrawal',
                    description: 'Multiple concurrent withdrawals processed',
                    impact: 'Double spending, financial loss'
                });
            }
        } catch (error) {
            // Expected without valid authentication
        }
    }

    /**
     * Test amount manipulation
     */
    async testAmountManipulation() {
        const maliciousAmounts = [
            -100,           // Negative amount
            0,              // Zero amount
            999999999999,   // Extremely large amount
            0.000000001,    // Extremely small amount
            'NaN',          // Not a number
            'Infinity',     // Infinity
            null,           // Null value
            undefined       // Undefined value
        ];
        
        for (const amount of maliciousAmounts) {
            try {
                const response = await this.makeRequest('POST', '/api/add-withdrawal', {
                    amount: amount,
                    address: '0x1234567890123456789012345678901234567890'
                });
                
                if (response.status === 200 && response.data.status === true) {
                    this.addVulnerability({
                        type: 'Amount Manipulation',
                        severity: 'High',
                        endpoint: '/api/add-withdrawal',
                        payload: { amount },
                        description: `Invalid amount accepted: ${amount}`,
                        impact: 'Financial manipulation, system abuse'
                    });
                }
            } catch (error) {
                // Expected for most invalid amounts
            }
        }
    }

    /**
     * Utility methods
     */
    async makeRequest(method, endpoint, data = {}, headers = {}) {
        const config = {
            method,
            url: `${this.baseUrl}${endpoint}`,
            timeout: this.scanConfig.timeout,
            headers: {
                'User-Agent': this.scanConfig.userAgent,
                'Content-Type': 'application/json',
                ...headers
            }
        };
        
        if (method.toUpperCase() !== 'GET') {
            config.data = data;
        } else {
            config.params = data;
        }
        
        return await axios(config);
    }

    createJWTWithAlgorithm(algorithm, payload) {
        const header = Buffer.from(JSON.stringify({ alg: algorithm, typ: 'JWT' })).toString('base64url');
        const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64url');
        
        if (algorithm === 'none') {
            return `${header}.${payloadStr}.`;
        }
        
        // For other algorithms, would need proper signing
        return `${header}.${payloadStr}.fake-signature`;
    }

    createJWTWithSecret(secret, payload) {
        const jwt = require('jsonwebtoken');
        return jwt.sign(payload, secret, { algorithm: 'HS256' });
    }

    addVulnerability(vulnerability) {
        vulnerability.id = crypto.randomUUID();
        vulnerability.timestamp = new Date().toISOString();
        this.vulnerabilities.push(vulnerability);
        
        log.warn(`Vulnerability detected: ${vulnerability.type}`, {
            severity: vulnerability.severity,
            endpoint: vulnerability.endpoint
        });
    }

    processTestResults(category, results) {
        const passed = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        this.testResults.push({
            category,
            total: results.length,
            passed,
            failed,
            timestamp: new Date().toISOString()
        });
    }

    async generateSecurityReport() {
        const report = {
            scanId: crypto.randomUUID(),
            timestamp: new Date().toISOString(),
            summary: {
                totalVulnerabilities: this.vulnerabilities.length,
                critical: this.vulnerabilities.filter(v => v.severity === 'Critical').length,
                high: this.vulnerabilities.filter(v => v.severity === 'High').length,
                medium: this.vulnerabilities.filter(v => v.severity === 'Medium').length,
                low: this.vulnerabilities.filter(v => v.severity === 'Low').length
            },
            vulnerabilities: this.vulnerabilities,
            testResults: this.testResults,
            recommendations: this.generateRecommendations()
        };
        
        // Save report to file
        const reportPath = path.join(__dirname, 'reports', `security-scan-${Date.now()}.json`);
        await fs.mkdir(path.dirname(reportPath), { recursive: true });
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
        
        log.info(`Security report generated: ${reportPath}`);
        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.vulnerabilities.some(v => v.type.includes('Injection'))) {
            recommendations.push({
                priority: 'Critical',
                title: 'Implement Input Validation',
                description: 'Use parameterized queries and input sanitization to prevent injection attacks'
            });
        }
        
        if (this.vulnerabilities.some(v => v.type.includes('JWT'))) {
            recommendations.push({
                priority: 'Critical',
                title: 'Strengthen JWT Security',
                description: 'Use strong secrets, validate algorithms, implement proper token expiration'
            });
        }
        
        if (this.vulnerabilities.some(v => v.type.includes('XSS'))) {
            recommendations.push({
                priority: 'High',
                title: 'Implement XSS Protection',
                description: 'Use output encoding, Content Security Policy, and input validation'
            });
        }
        
        return recommendations;
    }

    // Additional test methods would be implemented here...
    async testCommandInjection() { /* Implementation */ }
    async testPathTraversal() { /* Implementation */ }
    async testXMLInjection() { /* Implementation */ }
    async testLDAPInjection() { /* Implementation */ }
    async scanAuthorization() { /* Implementation */ }
    async scanApiSecurity() { /* Implementation */ }
    async scanFileUpload() { /* Implementation */ }
    async scanSessionManagement() { /* Implementation */ }
    async testPasswordPolicyBypass() { /* Implementation */ }
    async test2FABypass() { /* Implementation */ }
    async testSessionFixation() { /* Implementation */ }
    async testAccountEnumeration() { /* Implementation */ }
    async testWithdrawalBypass() { /* Implementation */ }
    async testDoubleSpending() { /* Implementation */ }
    async testFeeManipulation() { /* Implementation */ }
}

module.exports = VulnerabilityScanner;
