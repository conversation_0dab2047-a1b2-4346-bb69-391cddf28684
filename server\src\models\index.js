module.exports.adminModel = require('./admin.model');
module.exports.userModel = require('./user.model');
module.exports.userreModel = require('./userre.model');
module.exports.verificationModel = require('./verification.model');
module.exports.counterModel = require('./counter.model');
module.exports.depositModel = require('./deposit.model');
module.exports.fundDeductModel = require('./funddeduct.model');
module.exports.fundTransferModel = require('./fundtransfer.model');
module.exports.incomeModel = require('./income.model');
module.exports.investmentPlanModel = require('./investmentplan.model');
module.exports.investmentModel = require('./investment.model');
module.exports.messageModel = require('./message.model');
module.exports.rankModel = require('./rank.model');
module.exports.teamRewardModel = require('./teamreward.model');
module.exports.withdrawalModel = require('./withdrawal.model');
module.exports.settingModel = require('./setting.model');
module.exports.userLoginRequestModel = require('./user.login.request.model');
module.exports.socialLinksModel = require('./social.links.model');
module.exports.announcementModel = require('./announcement.model');
module.exports.tradeActivationModel = require('./trade.activation.model');
module.exports.cronExecutionModel = require('./cron.execution.model');