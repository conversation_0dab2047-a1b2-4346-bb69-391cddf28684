/* Animation definitions for trade components - Binance Theme */

/* Bottom to top floating animation */
@keyframes floatUp {
    0% {
        transform: translateY(0) scale(0.8);
        opacity: 0;
    }
    15% {
        transform: translateY(-20px) scale(1);
        opacity: 1;
    }
    85% {
        transform: translateY(-80vh) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) scale(0.8);
        opacity: 0;
    }
}

/* Pulsing border animation with Binance gold */
@keyframes pulseBorder {
    0% {
        transform: scale(1);
        opacity: 0.3;
        box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4);
    }
    50% {
        transform: scale(1.05);
        opacity: 0.6;
        box-shadow: 0 0 0 10px rgba(240, 185, 11, 0.1);
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
        box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
    }
}

/* Gold glow animation */
@keyframes goldGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(240, 185, 11, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(240, 185, 11, 0.6), 0 0 30px rgba(240, 185, 11, 0.4);
    }
}

/* Floating profit element */
.floating-profit {
    animation: floatUp 6s forwards cubic-bezier(0.25, 0.1, 0.25, 1) !important;
    will-change: transform, opacity;
}

/* Binance-themed trading elements */
.binance-pulse {
    animation: pulseBorder 2s infinite;
}

.binance-glow {
    animation: goldGlow 3s ease-in-out infinite;
}
