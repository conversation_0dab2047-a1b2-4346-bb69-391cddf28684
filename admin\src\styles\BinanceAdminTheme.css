/* Binance Admin Theme Enhancement CSS */
/* Complete styling for admin panel with Binance black and golden theme */

/* Admin-specific Binance Theme Variables */
:root {
  /* Admin color extensions */
  --admin-sidebar-width: 280px;
  --admin-header-height: 64px;
  --admin-gold-gradient: linear-gradient(135deg, #F0B90B 0%, #FCD535 100%);
  --admin-dark-gradient: linear-gradient(135deg, #1E2329 0%, #2B3139 100%);
  --admin-card-gradient: linear-gradient(145deg, #1E2329 0%, #2B3139 50%, #1E2329 100%);
  
  /* Admin shadows */
  --admin-shadow-sm: 0 2px 8px rgba(240, 185, 11, 0.1);
  --admin-shadow-md: 0 4px 16px rgba(240, 185, 11, 0.15);
  --admin-shadow-lg: 0 8px 32px rgba(240, 185, 11, 0.2);
  
  /* Admin borders */
  --admin-border-light: 1px solid rgba(240, 185, 11, 0.1);
  --admin-border-medium: 1px solid rgba(240, 185, 11, 0.3);
}

/* Admin Layout Components */
.admin-sidebar {
  background: var(--admin-dark-gradient) !important;
  border-right: var(--admin-border-light) !important;
  width: var(--admin-sidebar-width) !important;
  box-shadow: var(--admin-shadow-md) !important;
}

.admin-sidebar-header {
  padding: 20px 24px !important;
  border-bottom: var(--admin-border-light) !important;
  background: rgba(240, 185, 11, 0.05) !important;
}

.admin-logo {
  color: var(--binance-gold) !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.admin-nav-menu {
  padding: 16px 0 !important;
}

.admin-nav-item {
  display: flex !important;
  align-items: center !important;
  padding: 12px 24px !important;
  color: var(--binance-text-secondary) !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  border-left: 3px solid transparent !important;
  margin: 2px 0 !important;
}

.admin-nav-item:hover {
  color: var(--binance-gold) !important;
  background: rgba(240, 185, 11, 0.1) !important;
  border-left-color: rgba(240, 185, 11, 0.3) !important;
}

.admin-nav-item.active {
  color: var(--binance-gold) !important;
  background: rgba(240, 185, 11, 0.15) !important;
  border-left-color: var(--binance-gold) !important;
  font-weight: 600 !important;
}

.admin-nav-icon {
  margin-right: 12px !important;
  font-size: 1.2rem !important;
}

/* Admin Header */
.admin-header {
  background: var(--binance-dark-gray) !important;
  border-bottom: var(--admin-border-light) !important;
  height: var(--admin-header-height) !important;
  box-shadow: var(--admin-shadow-sm) !important;
}

.admin-header-content {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 0 24px !important;
  height: 100% !important;
}

.admin-header-title {
  color: var(--binance-text-primary) !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
}

.admin-header-actions {
  display: flex !important;
  align-items: center !important;
  gap: 16px !important;
}

/* Admin Cards */
.admin-card {
  background: var(--admin-card-gradient) !important;
  border: var(--admin-border-light) !important;
  border-radius: 16px !important;
  box-shadow: var(--admin-shadow-sm) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

.admin-card:hover {
  border-color: rgba(240, 185, 11, 0.3) !important;
  box-shadow: var(--admin-shadow-md) !important;
  transform: translateY(-2px) !important;
}

.admin-card-header {
  background: linear-gradient(90deg, rgba(240, 185, 11, 0.1) 0%, transparent 100%) !important;
  padding: 20px 24px !important;
  border-bottom: var(--admin-border-light) !important;
}

.admin-card-title {
  color: var(--binance-text-primary) !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.admin-card-content {
  padding: 24px !important;
}

/* Admin Tables */
.admin-table {
  background: var(--binance-dark-gray) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: var(--admin-shadow-sm) !important;
  border: var(--admin-border-light) !important;
}

.admin-table-header {
  background: var(--binance-medium-gray) !important;
  color: var(--binance-text-primary) !important;
  font-weight: 600 !important;
  padding: 16px !important;
  border-bottom: var(--admin-border-light) !important;
}

.admin-table-row {
  border-bottom: 1px solid rgba(43, 49, 57, 0.5) !important;
  transition: background 0.2s ease !important;
}

.admin-table-row:hover {
  background: rgba(240, 185, 11, 0.05) !important;
}

.admin-table-cell {
  padding: 16px !important;
  color: var(--binance-text-secondary) !important;
  border-bottom: 1px solid rgba(43, 49, 57, 0.3) !important;
}

/* Admin Buttons */
.admin-btn-primary {
  background: var(--admin-gold-gradient) !important;
  color: #000000 !important;
  border: none !important;
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: 8px !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--admin-shadow-sm) !important;
}

.admin-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--admin-shadow-md) !important;
  background: linear-gradient(135deg, #FCD535 0%, #F0B90B 100%) !important;
}

.admin-btn-secondary {
  background: transparent !important;
  color: var(--binance-gold) !important;
  border: var(--admin-border-medium) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: 8px !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease !important;
}

.admin-btn-secondary:hover {
  background: rgba(240, 185, 11, 0.1) !important;
  border-color: var(--binance-gold) !important;
}

.admin-btn-danger {
  background: var(--binance-red) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
  text-transform: none !important;
  border-radius: 8px !important;
  padding: 10px 20px !important;
  transition: all 0.3s ease !important;
}

.admin-btn-danger:hover {
  background: #D93D52 !important;
  transform: translateY(-1px) !important;
}

/* Admin Forms */
.admin-form-group {
  margin-bottom: 24px !important;
}

.admin-form-label {
  color: var(--binance-text-primary) !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  display: block !important;
}

.admin-form-input {
  background: rgba(30, 35, 41, 0.8) !important;
  border: var(--admin-border-light) !important;
  border-radius: 8px !important;
  color: var(--binance-text-primary) !important;
  padding: 12px 16px !important;
  width: 100% !important;
  transition: all 0.3s ease !important;
}

.admin-form-input:focus {
  border-color: var(--binance-gold) !important;
  box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.2) !important;
  background: rgba(30, 35, 41, 1) !important;
  outline: none !important;
}

.admin-form-input::placeholder {
  color: var(--binance-text-secondary) !important;
}

/* Admin Stats Cards */
.admin-stat-card {
  background: var(--admin-card-gradient) !important;
  border: var(--admin-border-light) !important;
  border-radius: 16px !important;
  padding: 24px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.admin-stat-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 4px !important;
  background: var(--admin-gold-gradient) !important;
}

.admin-stat-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: var(--admin-shadow-lg) !important;
  border-color: rgba(240, 185, 11, 0.3) !important;
}

.admin-stat-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  background: rgba(240, 185, 11, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--binance-gold) !important;
  margin-bottom: 16px !important;
  font-size: 1.5rem !important;
}

.admin-stat-value {
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--binance-text-primary) !important;
  margin: 8px 0 !important;
}

.admin-stat-label {
  color: var(--binance-text-secondary) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.admin-stat-change {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin-top: 8px !important;
}

.admin-stat-change.positive {
  color: var(--binance-green) !important;
}

.admin-stat-change.negative {
  color: var(--binance-red) !important;
}

/* Admin Status Badges */
.admin-status-active {
  background: rgba(14, 203, 129, 0.1) !important;
  color: var(--binance-green) !important;
  border: 1px solid rgba(14, 203, 129, 0.3) !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
}

.admin-status-inactive {
  background: rgba(246, 70, 93, 0.1) !important;
  color: var(--binance-red) !important;
  border: 1px solid rgba(246, 70, 93, 0.3) !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
}

.admin-status-pending {
  background: rgba(240, 185, 11, 0.1) !important;
  color: var(--binance-gold) !important;
  border: 1px solid rgba(240, 185, 11, 0.3) !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
}

/* Admin Modals */
.admin-modal {
  background: var(--binance-dark-gray) !important;
  border: var(--admin-border-medium) !important;
  border-radius: 16px !important;
  box-shadow: var(--admin-shadow-lg) !important;
  max-width: 600px !important;
  width: 90% !important;
}

.admin-modal-header {
  background: var(--binance-medium-gray) !important;
  padding: 20px 24px !important;
  border-bottom: var(--admin-border-light) !important;
  border-radius: 16px 16px 0 0 !important;
}

.admin-modal-title {
  color: var(--binance-text-primary) !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

.admin-modal-content {
  padding: 24px !important;
}

.admin-modal-footer {
  padding: 16px 24px !important;
  border-top: var(--admin-border-light) !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
}

/* Admin Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100% !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1000 !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
  }
  
  .admin-sidebar.open {
    transform: translateX(0) !important;
  }
  
  .admin-card-content {
    padding: 16px !important;
  }
  
  .admin-stat-card {
    padding: 16px !important;
  }
  
  .admin-stat-value {
    font-size: 1.5rem !important;
  }
}

/* Admin Utility Classes */
.admin-text-gold { color: var(--binance-gold) !important; }
.admin-text-green { color: var(--binance-green) !important; }
.admin-text-red { color: var(--binance-red) !important; }
.admin-bg-gold { background-color: var(--binance-gold) !important; }
.admin-bg-dark { background-color: var(--binance-dark-gray) !important; }
.admin-border-gold { border-color: var(--binance-gold) !important; }
