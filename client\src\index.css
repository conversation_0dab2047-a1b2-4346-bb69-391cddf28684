:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Binance-inspired color variables */
  --binance-gold: #F0B90B;
  --binance-gold-light: #FCD535;
  --binance-gold-dark: #D4A200;
  --binance-black: #0B0E11;
  --binance-dark-gray: #1E2329;
  --binance-medium-gray: #2B3139;
  --binance-green: #0ECB81;
  --binance-red: #F6465D;
  --binance-text-primary: #FFFFFF;
  --binance-text-secondary: #B7BDC6;

  color-scheme: light dark;
  color: var(--binance-text-primary);
  background-color: var(--binance-black);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--binance-gold);
  text-decoration: inherit;
  transition: color 0.2s ease;
}
a:hover {
  color: var(--binance-gold-light);
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--binance-dark-gray);
  color: var(--binance-text-primary);
  cursor: pointer;
  transition: all 0.25s ease;
}
button:hover {
  border-color: var(--binance-gold);
  background-color: var(--binance-medium-gray);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--binance-gold);
  outline-offset: 2px;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #1E2329;
    background-color: #FAFAFA;
  }
  a:hover {
    color: var(--binance-gold-dark);
  }
  button {
    background-color: #F5F5F5;
    color: #1E2329;
  }
  button:hover {
    background-color: #E0E0E0;
  }
}
