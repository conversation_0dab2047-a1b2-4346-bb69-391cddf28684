{"name": "node-project", "version": "1.0.0", "description": "Node Project", "main": "index.js", "scripts": {"start": "npm run start:prod", "start:dev": "nodemon index.js", "start:prod": "npm install && node index.js", "lint": "eslint src/**/*.js", "lint-fix": "eslint src/**/*.js --fix", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": "18.x"}, "repository": {"type": "git", "url": ""}, "author": "ABC", "license": "ISC", "dependencies": {"@hapi/joi": "^16.1.7", "@hapi/joi-date": "^2.0.1", "async": "^3.2.5", "aws-sdk": "^2.1477.0", "axios": "^0.19.2", "bcryptjs": "^2.4.3", "bignumber.js": "^9.2.1", "body-parser": "^1.19.0", "bunyan": "^1.8.12", "child_process": "^1.0.2", "cjson": "^0.5.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "ejs": "^3.1.5", "elliptic": "^6.6.1", "ethers": "^6.13.2", "express": "^4.17.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.0", "google-auth-library": "^5.5.1", "graceful-fs": "^4.2.11", "helmet": "^7.1.0", "hpp": "^0.2.3", "js-sha512": "^0.8.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^8.5.1", "jwt-decode": "^2.2.0", "keccak256": "^1.0.6", "multer": "^1.4.4", "multer-s3": "^2.10.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.6", "otplib": "^12.0.1", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "qrcode": "^1.5.3", "request": "^2.88.2", "sequelize": "^6.37.5", "shelljs": "^0.8.5", "uuid": "^3.3.3", "web3": "^4.16.0", "ws": "^8.18.2", "xss-clean": "^0.1.1"}, "devDependencies": {"babel-eslint": "^10.0.3", "eslint": "^6.6.0", "firebase-admin": "^9.4.2", "mongoose": "^6.12.3", "nodemon": "^1.19.4"}}