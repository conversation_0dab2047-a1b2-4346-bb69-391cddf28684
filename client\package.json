{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.1.0", "axios": "^1.8.4", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "iconsax-react": "^0.0.8", "jwt-decode": "^4.0.0", "lucide-react": "^0.509.0", "notistack": "^3.0.2", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-number-format": "^5.4.4", "react-qr-reader": "^3.0.0-beta-1", "react-router-dom": "^6.30.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "sweetalert2": "^11.19.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "terser": "^5.39.0", "vite": "^6.3.1"}}